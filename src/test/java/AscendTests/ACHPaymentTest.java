package AscendTests;

import ascendControllers.*;
import ascendControllers.OverviewScreenController;
import ascendControllers.PayNowPaymentDetailsController;
import base.Base;
import common.GlobalUtil;
import controlller.*;
import org.testng.Assert;
import org.testng.SkipException;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.io.IOException;


public class ACHPaymentTest extends Base {

	OverviewScreenController overviewScreenController;
	PayNowCheckingSavingsScreenController payNowCheckingsSavingsScreenController;
	PayNowSelectPaymentMethodScreenController payNowSelectPaymentMethodScreenControllerScreenController;
	AddNewCheckingSavingsController addNewCheckingSavingsController;
	PayNowPaymentDetailsController payNowPaymentDetailsController;
	PayNowNotificationController payNowNotificationController;
	PayNowReviewPaymentController payNowReviewPaymentController;
	PayNowConfirmationController payNowConfirmationController;
	String amount = "$6.00";
	String totalDue;

	GlobalUtil globalUtil = new GlobalUtil();


	String accountNumber = globalUtil.selectAccountRandomly();
	String accountType = "Savings";
	String emailNotification;
	String phoneNotification;
	boolean isLoginSuccessful = false;


	@BeforeClass
	public void setupPaymentConfirmationScreenTest() throws InterruptedException, IOException {
		setUp();
		signOutWhenLoginScreenDoesNotAppear();
		LoginController loginController = new LoginController();
		loginController.enterNewCredentials(envProperties.getProperty("ascendACHAccount"));
		isLoginSuccessful = loginController.verifyLoginIsSuccessfull();
		if (!isLoginSuccessful) {
			log.error("Login failed. Skipping all tests.");
		} else {
			log.info("Login successful");
		}

	}

	@BeforeMethod
	public void initializeControllers() throws InterruptedException, IOException {
		if (!isLoginSuccessful) {
			throw new SkipException("Skipping test because login was not successful.");
		}
		overviewScreenController = new OverviewScreenController();
		payNowSelectPaymentMethodScreenControllerScreenController = new PayNowSelectPaymentMethodScreenController();
		payNowCheckingsSavingsScreenController = new PayNowCheckingSavingsScreenController();
		addNewCheckingSavingsController = new AddNewCheckingSavingsController();
		payNowPaymentDetailsController = new PayNowPaymentDetailsController();
		payNowNotificationController = new PayNowNotificationController();
		payNowReviewPaymentController = new PayNowReviewPaymentController();
		payNowConfirmationController = new PayNowConfirmationController();
	}

	@Test(priority = 1)
	public void verifyPaymentDetailsPage() throws IOException {
		overviewScreenController.clickBtnPayNow();
		payNowSelectPaymentMethodScreenControllerScreenController.clickCheckingAndSavingsAccountAndContinue();
		payNowCheckingsSavingsScreenController.clickAddNewCheckingSavingsAccountLink();
		String nickName = globalUtil.randomString(5);
		addNewCheckingSavingsController.addBankAccountWithoutSaving(nickName, accountNumber, accountType);
		payNowCheckingsSavingsScreenController.waitForCheckingsSavingsPageToLoad();
		payNowCheckingsSavingsScreenController.clickOnContinueBtnCheckingSavingsPage();
		totalDue = payNowPaymentDetailsController.getTotalDue();
		payNowPaymentDetailsController.clickOnOtherAmount(amount);
		payNowPaymentDetailsController.clickOnPaymentDetailsContinueBtn();
	}

	@Test(priority = 2)
	public void verifyNotificationPage() throws IOException {
		emailNotification = payNowNotificationController.getEmailNotification();
		phoneNotification = payNowNotificationController.getPhoneNotification();
	}

	@Test(priority = 3)
	public void verifyTheContentOnReviewPayment() {
		payNowNotificationController.clickOnReviewPaymentButton();
		Assert.assertTrue(payNowReviewPaymentController.verifyPaymentMethod(accountType));
		Assert.assertTrue(payNowReviewPaymentController.verifyAmount(amount));
		Assert.assertTrue(payNowReviewPaymentController.verifyDate());
		Assert.assertTrue(payNowReviewPaymentController.verifyEmailNotification(emailNotification));
		Assert.assertTrue(payNowReviewPaymentController.verifyPhoneNotification(phoneNotification));
		Assert.assertTrue(payNowReviewPaymentController.verifyConfirmPaymentButton());
		Assert.assertTrue(payNowReviewPaymentController.verifyPaymentDisclaimerCopy());

	}

	@Test(priority = 9)
	public void NavigationOfConfirmPaymentPage() {
		payNowReviewPaymentController.clickOnConfirmPaymentButton();
	}


	@Test(priority = 11)
	public void verifyConfirmationPageSection() {
		Assert.assertTrue(payNowConfirmationController.verifyPaymentSuccessMessage());
		Assert.assertTrue(payNowConfirmationController.verifyConfirmationNumber());
		Assert.assertTrue(payNowConfirmationController.verifyGoBackToOverviewLink());
		Assert.assertTrue(payNowConfirmationController.verifyConfirmationNotificationCopy());
		Assert.assertTrue(payNowConfirmationController.verifyEmailNotificationOnConfirmationPage(emailNotification));
		Assert.assertTrue(payNowConfirmationController.verifyPhoneNotificationOnConfirmationPage(phoneNotification));
		Assert.assertTrue(payNowConfirmationController.verifyPaymentMethodBladeOnConfirmationPage());
		Assert.assertTrue(payNowConfirmationController.verifyPaymentMethodCopyOnConfirmationPage(accountType));
		Assert.assertTrue(payNowConfirmationController.verifyAmountPaidBladeOnConfirmationPage());
		Assert.assertTrue(payNowConfirmationController.verifyAmountPaidCopyOnConfirmationPage(amount));
		Assert.assertTrue(payNowConfirmationController.verifyPaymentDateBladeOnConfirmationPage());
		Assert.assertTrue(payNowConfirmationController.verifyPaymentDateOnConfirmationPage());
		Assert.assertTrue(payNowConfirmationController.verifyRemainingBalanceBladeOnConfirmationPage());
		Assert.assertTrue(payNowConfirmationController.verifyRemainingBalanceOnConfirmationPage(amount, totalDue));
		Assert.assertTrue(payNowConfirmationController.verifyPaymentConfirmationDisclaimerCopyOnConfirmationPage());

	}


	@Test(priority = 19)
	public void NavigationOfOverviewPage() {
		payNowConfirmationController.clickOnGoBackToOverviewLink();
		Assert.assertTrue(overviewScreenController.isOverviewLandingPageLoaded());
	}
}
