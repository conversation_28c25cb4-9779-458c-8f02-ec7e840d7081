package ascendPageObjects;

import base.Base;
import io.appium.java_client.AppiumBy;
import org.openqa.selenium.By;

import static io.appium.java_client.AppiumBy.iOSClassChain;
import static io.appium.java_client.AppiumBy.iOSNsPredicateString;
import static org.openqa.selenium.By.id;
import static org.openqa.selenium.By.xpath;

public class PayNowConfirmationPageObjects extends Base {
	public By dismissButton;
	public By paymentSuccessMessage;
	public By closeButton;
	public By copyConfirmationNumber;
	public By linkGoBackToOverview;
	public By confirmationNotificationMessage;
	public By emailNotificationConfirmPaymentPg;
	public By phoneNotificationConfirmPaymentPg;
	public By bladePaymentMethod;
	public By copyPaymentMethod;
	public By bladeAmountPaid;
	public By moneyAmountPaid;
	public By bladePaymentDate;
	public By copyPaymentDate;
	public By paymentDate;
	public By bladeRemainingBalance;
	public By moneyRemainingBalance;
	public By paymentConfirmationDisclaimer;
	public By paymentConfirmationDisclaimerCreditCard;

	public void androidElements() {
		dismissButton = id("txt_due_amount");
	}

	public void iosElements() {
		dismissButton = xpath("//XCUIElementTypeButton[@name=\"dialogCloseButton\"]");
		paymentSuccessMessage = xpath("(//XCUIElementTypeStaticText[@name='Payment submitted successfully!'])[2]");
		copyConfirmationNumber = xpath("(//XCUIElementTypeStaticText[@name='successCode'])[2]");
		linkGoBackToOverview = xpath("(//XCUIElementTypeButton[@name=\"Go Back to Home\"])[2]");
		confirmationNotificationMessage = iOSClassChain("**/XCUIElementTypeStaticText[`label == 'A confirmation of this payment has been sent to:'`][2]");
		emailNotificationConfirmPaymentPg = xpath("(//XCUIElementTypeStaticText[@name=\"A confirmation of this payment has been sent to:\"])[2]//following-sibling::XCUIElementTypeStaticText[1]");
		phoneNotificationConfirmPaymentPg = xpath("(//XCUIElementTypeStaticText[@name=\"A confirmation of this payment has been sent to:\"])[2]//following-sibling::XCUIElementTypeStaticText[2]");
		bladePaymentMethod = iOSNsPredicateString("label == 'Payment Method'");
		copyPaymentMethod = xpath("(//*[@name='Payment Method'])[2]");
		bladeAmountPaid = iOSNsPredicateString("label == 'Amount Paid '");
		moneyAmountPaid = xpath("(//XCUIElementTypeStaticText[@name='Amount Paid '])[2]");
		bladePaymentDate = iOSNsPredicateString("label == 'Payment Date'");
		copyPaymentDate = xpath("(//XCUIElementTypeStaticText[@name=\"Payment Date\"])[2]");
		paymentDate = xpath("//*[@name='Payment Date'][2]");
		bladeRemainingBalance = iOSNsPredicateString("label == 'Remaining Balance'");
		moneyRemainingBalance = xpath("(//XCUIElementTypeStaticText[@name='Remaining Balance'])[2]");
		paymentConfirmationDisclaimer = xpath("//XCUIElementTypeOther[@name=\"Payments will automatically reflect on your account; however, the payment may take up to 3 business days to deduct from your bank account. Your payment will reflect as “Pending Payment” until we receive the funds from your bank.\n" +
				"\n" +
				"You have scheduled a payment after the due date. A late fee may be assessed.\"]");
		paymentConfirmationDisclaimerCreditCard = xpath("//XCUIElementTypeStaticText[@name=\"Payments will automatically reflect on your account; however, the payment may take up to 3 business days to deduct from your bank account. Your payment will reflect as “Pending Payment” until we receive the funds from your bank.\"]");

	}


}
