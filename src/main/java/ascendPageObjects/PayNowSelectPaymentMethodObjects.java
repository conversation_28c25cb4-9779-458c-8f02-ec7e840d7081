package ascendPageObjects;

import base.Base;
import io.appium.java_client.AppiumBy;
import org.openqa.selenium.By;

import static io.appium.java_client.AppiumBy.iOSClassChain;
import static io.appium.java_client.AppiumBy.iOSNsPredicateString;
import static org.openqa.selenium.By.id;
import static org.openqa.selenium.By.xpath;

public class PayNowSelectPaymentMethodObjects extends Base {
	public By checkingAndSavingsAcc;
	public By continueBtn;

	/**
	 * Not implemented yet
	 */
	public By accountInformationTile;
	public By progressMeter;
	public By checkingOrSavingsAccounts;
	public By dropdownPaymentMethod;
	public By linkAddNewCheckingSavingsAccount;
	public By enrollInAutoPay;
	public By linkTermsOfService;
	public By checkboxEnrollInAutoPay;
	public By otherPaymentMethods;
	public By creditCard;
	public By debitCard;
	public By btnContinue;
	public By unCheckboxEnrollInAutoPay;
	public By copyEnrollInAutoPay;
	public By creditCardPopupHeader;
	public By btnCreditCardCancel;
	public By btnCreditCardContinue;
	public By debitCardPopupHeader;
	public By btnDebitCardCancel;
	public By btnDebitCardContinue;
	public By accountNameAndAddress;
	public By payNowBackIcon;
	public By newCheckingsSavingsAccountHeader;
	public By paymentDetailsScreen;
	public By creditCardButton;
	public By debitCardButton;
	public By pageTitleTermsOfService;
	public By autoPayDescriptionText;
	public By progressMeter2;
	public By payNowTextHeader;
	public By checkingAccountDropDown;

	public By dismissPaymentProcessPopUp;

	public String comparePageTitleTermsOfService;


	public By btnContinuePayNow;
	public By billMatrixPopUpCopyCreditCard;
	public By billMatrixPopUpCopyDeditCard;
	public By accountDropDown;
	public By selectPaymentMethod;
	public By checkingAndSavingsAccUnselected;
	public By creditCardUnselected;
	public By debitCardUnselected;
	public By checkingAndSavingsAccSelected;
	public By creditCardSelected;
	public By debitCardSelected;
	public By btnContinueUnselected;
	public By btnContinueSelected;
	public By continueBtnPayNow;
	public By continueBtnOnCheckingSavingsAccount;
	public By visualStepper1;
	public By visualStepper2;
	public By visualStepper3;
	public By visualStepper4;
	public By dropDownCheckingOrSavings;
	public By totalDue;
	public By dialogText;
	//   public String compareDialogText;

	public By venmo;
	public By payPal;

	public By gPay;

	public By amazonPay;

	public By applePay;

	public By velocityExceededMsg;


	public By PayArrangementAmountText;
	public By PayArrangementAmountValue;
	public By paymentArrangementDisclaimer;

	public By btnContinueAddAccount;
	public By paymentDetailsContinueBtn;

	public By prePayRemainingDollars;
	public By prePayRemainingDays;


	public void androidElements() {
		checkingAndSavingsAcc = xpath("//*[@text='Checking & Savings Account']");
		continueBtn = new AppiumBy.ByAccessibilityId("Continue");
		/**
		 * Not implemented yet
		 */
		otherPaymentMethods = id("other_payment_method_text");
		//btnContinue=id("continue_button");
		copyEnrollInAutoPay = id("auto_pay_description_text");
		newCheckingsSavingsAccountHeader = xpath("//*[@text='New Checking/Savings Account']");
		//  comparePageTitleTermsOfService = excelUtility.getStringData("PayNow", 10, 0);
		accountDropDown = id("selected_account_layout");
		selectPaymentMethod = id("txt_select_payment_method");
		checkingAndSavingsAccUnselected = xpath("//*[@content-desc='Checking & Savings Account not selected']");
		creditCardUnselected = xpath("//android.widget.TextView[contains(@resource-id, \"id/txt_payment_method_title\") and @text=\"Credit Card\"]");
		debitCardUnselected = xpath("//*[@content-desc='Debit Card not selected']");
		//  btnContinue=id("continue_button");
		btnContinue = xpath("//*[@text='Continue']");
		checkingAndSavingsAccSelected = xpath("//*[@content-desc='Checking & Savings Account selected']");
		creditCardSelected = xpath("//*[@content-desc='Credit Card selected']");
		debitCardSelected = xpath("//*[@content-desc='Debit Card selected']");
		checkingOrSavingsAccounts = xpath("//*[@text='Checking or Savings Account']");
		creditCardButton = xpath("//*[@text='Credit Card']");
		debitCardButton = xpath("//*[@text='Debit Card']");
		dialogText = id("txt_dialog_text");
		visualStepper1 = id("step_one");
		visualStepper2 = id("step_two");
		visualStepper3 = id("step_three");
		visualStepper4 = id("step_four");
		linkAddNewCheckingSavingsAccount = xpath("//*[@text='Add New Checking/Savings Account']");
		enrollInAutoPay = xpath("//android.widget.CheckBox[@content-desc='Enroll in Auto Pay.']");
		linkTermsOfService = xpath("//*[@content-desc='Terms of Service']");
		autoPayDescriptionText = id("auto_pay_description_text");
		dropdownPaymentMethod = id("payment_method_input");
		dropDownCheckingOrSavings = id("payment_method_input");
		pageTitleTermsOfService = xpath("//*[@text='Terms of Service']");
		payNowBackIcon = xpath("//*[@content-desc='Back']");
		//payNowBackIcon=id("btn_back");
		debitCardPopupHeader = xpath("//*[@text='Debit card payments can be made online through BillMatrix.']");
		creditCardPopupHeader = xpath("//*[@text='Credit card payments can be made online through BillMatrix.']");
		btnCreditCardCancel = xpath("//*[@text='Cancel']");
		btnCreditCardContinue = xpath("//*[@text='Continue']");
		btnDebitCardCancel = xpath("//*[@text='Cancel']");
		btnDebitCardContinue = xpath("//*[@text='Continue']");
		paymentDetailsScreen = xpath("//*[@text='Payment Details']");
		payNowTextHeader = xpath("//*[@text='Pay Now']");
		venmo = xpath("\n" +
				"(//android.view.ViewGroup[@content-desc=\"Not selected\"])[4] | //android.view.ViewGroup[@content-desc=\"Venmo not selected\"]\n");
		payPal = xpath("\n" +
				"(//android.view.ViewGroup[@content-desc=\"Not selected\"])[5] | //android.view.ViewGroup[@content-desc=\"PayPal not selected\"]\n");
		gPay = xpath("(//android.view.ViewGroup[@content-desc=\"Not selected\"])[4] | //android.view.ViewGroup[@content-desc=\"Google Pay not selected\"]\n");
		velocityExceededMsg = id("emergency_banner");
		btnContinuePayNow = id("continue_button");
		PayArrangementAmountText = id("textview_arrangement_amount_title");
		PayArrangementAmountValue = id("textview_arrangement_amount");
		paymentArrangementDisclaimer = id("textview_info_message");
		amazonPay = xpath("(//android.view.ViewGroup[@content-desc=\"Not selected\"])[6] | //android.view.ViewGroup[@content-desc=\"Amazon Pay not selected\"]\n");
		dismissPaymentProcessPopUp = new AppiumBy.ByAccessibilityId("Close");
		paymentDetailsContinueBtn = id("button_continue");
		continueBtnOnCheckingSavingsAccount = id("continue_button");
		totalDue = id("txt_due_amount");

	}

	public void iosElements() {
		checkingAndSavingsAcc = new AppiumBy.ByAccessibilityId("Checking & Savings Account");
		continueBtn = new AppiumBy.ByAccessibilityId("Button");
		/**
		 * Not implemented yet
		 */
		accountInformationTile = iOSNsPredicateString("label == 'Account Information'");
		accountNameAndAddress = xpath("//XCUIElementTypeStaticText[@name='acnt_name']");
		otherPaymentMethods = iOSNsPredicateString("label == 'Other Payment Methods'");
		creditCard = iOSNsPredicateString("name == \"Credit Card\"");
		btnContinuePayNow = iOSNsPredicateString("name == 'Continue button selected'");
		creditCardButton = xpath("(//*[@type='XCUIElementTypeButton'])[6]");
		debitCard = iOSNsPredicateString("name == 'Debit Card'");
		debitCardButton = iOSClassChain("**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView/XCUIElementTypeOther[1]/XCUIElementTypeOther[3]/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther/XCUIElementTypeOther[4]/XCUIElementTypeButton");
		btnContinue = xpath("//XCUIElementTypeButton[contains(@name, 'Continue')]");
		btnContinueAddAccount = xpath("(//XCUIElementTypeButton[@name=\"Continue\"])[2]");
		//btnContinue=xpath("");
		copyEnrollInAutoPay = iOSNsPredicateString("label == 'A zero balance is required to enroll in Auto Pay. If you wish to pay a different amount, uncheck the Enroll in Auto Pay checkbox and update payment amount.'");
		creditCardPopupHeader = iOSNsPredicateString("label == 'Credit Card payments can be made online through BillMatrix.'");
		btnCreditCardCancel = iOSClassChain("**/XCUIElementTypeButton[`label == 'Cancel'`]");
		btnCreditCardContinue = xpath("//XCUIElementTypeButton[contains(@name, \"Continue\")]\n");
		billMatrixPopUpCopyCreditCard = xpath("//*[@value='This is separate company not affiliated with Alabama Power.\n" +
				"\n" +
				"There is a maximum amount of $1000.00 that can be charged at one time to a credit card with a limit of ten (10) transactions in a 30-day period.\n" +
				"\n" +
				"You will need your Alabama Power account number along with a valid credit card to submit your payment seven days a week, 24 hours a day.']");
		debitCardPopupHeader = iOSNsPredicateString("label == 'Debit card payments can be made online through BillMatrix.'");
		btnDebitCardCancel = iOSClassChain("**/XCUIElementTypeButton[`label == 'Cancel'`]");
		btnDebitCardContinue = iOSClassChain("**/XCUIElementTypeButton[`label == 'Continue'`][2]");
		billMatrixPopUpCopyDeditCard = iOSNsPredicateString("value == \"This is separate company not affiliated with Alabama Power.\n" +
				"\n" +
				"Submit your payments seven days a week, 24 hours a day with a valid debit card.\"");
		newCheckingsSavingsAccountHeader = iOSNsPredicateString("label == 'New Checking/Savings Account'");
		accountDropDown = xpath("(//*[@type='XCUIElementTypeButton'])[3]");
		selectPaymentMethod = iOSNsPredicateString("label == 'Select a Payment Method'");
		checkingAndSavingsAccUnselected = iOSClassChain("**/XCUIElementTypeCell[`label == 'Checking & Savings Accountunselected'`]/XCUIElementTypeOther/XCUIElementTypeOther");
		creditCardUnselected = xpath("//XCUIElementTypeCell[@name=\"Credit Cardunselected\"]/XCUIElementTypeOther");
		debitCardUnselected = iOSClassChain("**/XCUIElementTypeCell[`label == 'Debit Cardunselected'`]/XCUIElementTypeOther/XCUIElementTypeOther");
		btnContinueUnselected = iOSClassChain("**/XCUIElementTypeButton[`label == 'Continue button unselected'`]");
		checkingAndSavingsAccSelected = iOSClassChain("**/XCUIElementTypeCell[`label == 'Checking & Savings Accountselected'`]/XCUIElementTypeOther/XCUIElementTypeOther");
		creditCardSelected = iOSClassChain("**/XCUIElementTypeCell[`label == 'Credit Cardselected'`]/XCUIElementTypeOther/XCUIElementTypeOther");
		debitCardSelected = iOSClassChain("**/XCUIElementTypeCell[`label == 'Debit Cardselected'`]/XCUIElementTypeOther/XCUIElementTypeOther");
		btnContinueSelected = iOSClassChain("**/XCUIElementTypeButton[`label == 'Continue button selected'`]");
		checkingOrSavingsAccounts = iOSNsPredicateString("label == 'Checking or Savings Account'");
		dialogText = xpath("//*[@name='Debit card payments can be made online through BillMatrix.']/following-sibling::XCUIElementTypeStaticText");
		visualStepper1 = iOSClassChain("**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView/XCUIElementTypeOther[1]/XCUIElementTypeOther[1]/XCUIElementTypeOther/XCUIElementTypeOther[1]");
		visualStepper2 = iOSClassChain("**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView/XCUIElementTypeOther[1]/XCUIElementTypeOther[1]/XCUIElementTypeOther/XCUIElementTypeOther[2]");
		visualStepper3 = iOSClassChain("**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView/XCUIElementTypeOther[1]/XCUIElementTypeOther[1]/XCUIElementTypeOther/XCUIElementTypeOther[3]");
		visualStepper4 = iOSClassChain("**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView/XCUIElementTypeOther[1]/XCUIElementTypeOther[1]/XCUIElementTypeOther/XCUIElementTypeOther[4]");
		linkAddNewCheckingSavingsAccount = xpath("//XCUIElementTypeStaticText[@name='Add New Checking/Savings Account']");
		enrollInAutoPay = iOSNsPredicateString("label == 'Enroll in Auto Pay.'");
		linkTermsOfService = iOSClassChain("**/XCUIElementTypeButton[`label == 'Terms of Service'`]");
		unCheckboxEnrollInAutoPay = iOSNsPredicateString("label == 'checkbox unchecked'");
		checkboxEnrollInAutoPay = iOSNsPredicateString("label == 'checkbox checked'");
		dropdownPaymentMethod = xpath("//*[@label='Checking or Savings Account']/parent::XCUIElementTypeOther/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther");
		dropDownCheckingOrSavings = xpath("//XCUIElementTypeButton[@name=\"Add New Checking/Savings Account\"]/preceding-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther");
		pageTitleTermsOfService = iOSNsPredicateString("label == 'Terms of Service'");
		payNowBackIcon = iOSNsPredicateString("label == 'iconBackArrow'");
		paymentDetailsScreen = iOSNsPredicateString("name == 'Payment Details'");
		payNowTextHeader = iOSNsPredicateString("label == 'Pay Now'");
		venmo = iOSNsPredicateString("name == \"venmo\"");
		payPal = iOSNsPredicateString("name == \"payPal\"");
		gPay = iOSNsPredicateString("name == \"googlePay\"");
		applePay = iOSNsPredicateString("name == \"applePay\"");
		velocityExceededMsg = new AppiumBy.ByAccessibilityId("You have reached the maximum number of authorized payments using this payment method. Please choose Checking or Savings (if available), Debit, or Credit Card to submit a payment.");
		PayArrangementAmountText = xpath("//XCUIElementTypeStaticText[@name=\"Pay Arrangement Amount\"]");
		PayArrangementAmountValue = xpath("//XCUIElementTypeStaticText[@name=\"Pay Arrangement Amount\"]/following-sibling::XCUIElementTypeStaticText[2]");
		paymentArrangementDisclaimer = xpath("//XCUIElementTypeStaticText[contains(@name, \"You must pay at least\")]");
		amazonPay = iOSNsPredicateString("name == 'amazonPay'");
		dismissPaymentProcessPopUp = xpath("//XCUIElementTypeButton[@name=\"Close\"]");
		checkingAccountDropDown = xpath("\n" +
				"//XCUIElementTypeOther[contains(@name, 'dropdown')]\n");
		totalDue = xpath("//XCUIElementTypeStaticText[@name=\"Total Due\"]/following-sibling::XCUIElementTypeStaticText[1]");
		continueBtnPayNow = xpath("//XCUIElementTypeButton[contains(@name, \"Continue button\")]");
		continueBtnOnCheckingSavingsAccount = xpath("//XCUIElementTypeButton[@name=\"Continue\"]");
		checkingAndSavingsAcc = xpath("//XCUIElementTypeStaticText[@name=\"Checking & Savings Account\"]");
		paymentDetailsContinueBtn = xpath("//XCUIElementTypeButton[@name=\"Continue\"]");
		prePayRemainingDollars = xpath("//XCUIElementTypeStaticText[@name=\"Dollars Remaining\"]/following-sibling::XCUIElementTypeStaticText[1]");
		prePayRemainingDays = xpath("//XCUIElementTypeStaticText[@name=\"Approximate Days Remaining\"]/following-sibling::XCUIElementTypeStaticText[1]");
	}


}
