package ascendPageObjects;

import base.Base;
import io.appium.java_client.AppiumBy;
import org.openqa.selenium.By;

import static io.appium.java_client.AppiumBy.*;

public class AddNewCheckingSavingsModal extends Base {
	public By ddBankAccountType;
	public By bankAccountTypeChecking;
	public By bankAccountTypeSavings;
	public By textBoxNameOnBankAccount;
	public By textBoxNickName;
	public By toolTipAddPaymentMethodInfo;
	public By textBoxRoutingNumber;
	public By textBoxBankAccountNumber;


	public By btnContinue;


	public void androidElements() {
		ddBankAccountType = xpath("//android.widget.Spinner[@text=\"Select Account\"]");
		bankAccountTypeChecking = xpath("//android.widget.TextView[@text=\"Checking\"]");
		bankAccountTypeSavings = xpath("//android.widget.TextView[@text=\"Savings\"]");
		textBoxNameOnBankAccount = id("name_on_bank_account_input");
		textBoxNickName = xpath("//android.widget.TextView[@text=\"Payment Method Nickname (Optional)\"]/following-sibling::android.widget.EditText");
		toolTipAddPaymentMethodInfo = id("add_payment_method_info");
		textBoxRoutingNumber = xpath("//android.widget.TextView[@text=\"Routing/Transit Number *\"]/following-sibling::android.widget.EditText");
		textBoxBankAccountNumber = xpath("//android.widget.TextView[@text=\"Bank Account Number *\"]/following-sibling::android.widget.EditText");
		btnContinue = xpath("//*[@text='Continue']");


	}

	public void iosElements() {
		ddBankAccountType = xpath("//XCUIElementTypeStaticText[@name=\"Bank Account Type *\"]/following-sibling::XCUIElementTypeButton");
		textBoxNameOnBankAccount = xpath("//*[@name='Name on Bank Account *']/following-sibling::XCUIElementTypeTextField");
		textBoxNickName = xpath("//*[@name='Payment Method Nickname (Optional)']/following-sibling::XCUIElementTypeTextField");
		toolTipAddPaymentMethodInfo = xpath("//*[@name='Payment Method Nickname (Optional)']/preceding-sibling::XCUIElementTypeStaticText");
		textBoxRoutingNumber = xpath("//*[@name='Routing Number *']/following-sibling::XCUIElementTypeTextField");
		textBoxBankAccountNumber = xpath("//*[@name='Bank Account Number *']/following-sibling::XCUIElementTypeTextField");
		btnContinue = new AppiumBy.ByAccessibilityId("Button");


	}
}
