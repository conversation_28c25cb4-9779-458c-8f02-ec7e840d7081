package ascendControllers;

import ascendPageObjects.OverviewScreenObjects;
import base.Base;
import common.AndroidMethods;
import common.IOSMethods;

import java.io.IOException;

public class OverviewScreenController extends Base {
	public static String driverInstance;
	AndroidMethods androidMethods;
	IOSMethods iosMethods;
	OverviewScreenObjects overviewScreenObjects = new OverviewScreenObjects();

	public OverviewScreenController() throws IOException {
		// Get the current thread's driver
		if (Base.getAndroidDriver() != null) {
			androidMethods = new AndroidMethods(Base.getAndroidDriver());
			driverInstance = androidDriverInstance;
			overviewScreenObjects.androidElements();

		} else if (Base.getIOSDriver() != null) {
			iosMethods = new IOSMethods(Base.getIOSDriver());
			driverInstance = iOSDriverInstance;
			overviewScreenObjects.iosElements();

		} else {
			log.error("No valid driver instance found for Android or iOS.");
			throw new IllegalStateException("No valid driver instance found for Android or iOS.");

		}
	}


	public void clickBtnPayNow() {
		if (isAndroid(Base.getAndroidDriver())) {
			if (androidMethods != null) {
				androidMethods.isElementPresent(overviewScreenObjects.btnPayNow);
				androidMethods.isElementClickable(overviewScreenObjects.btnPayNow, "overviewScreenObjects.btnPayNow");
				androidMethods.tapAsWebElement(overviewScreenObjects.btnPayNow);
			}
		} else {
			if (iosMethods != null) {
				iosMethods.isElementPresent(overviewScreenObjects.btnPayNow);
				iosMethods.isElementClickable(overviewScreenObjects.btnPayNow, "overviewScreenObjects.btnPayNow");
				iosMethods.tapAsWebElement(overviewScreenObjects.btnPayNow);
			}
		}
	}

	public boolean isOverviewLandingPageLoaded() {
		boolean isPresent = false;
		if (isAndroid(Base.getAndroidDriver())) {
			if (androidMethods != null) {
				try {
					androidMethods.staticWait(8000);
					isPresent = androidMethods.isElementDisplayed(overviewScreenObjects.labelTotalDue);
				} catch (Exception e) {
					System.out.println(e.getMessage());
				}
			}
		} else {
			if (iosMethods != null) {
				try {
					iosMethods.staticWait(8000);
					isPresent = iosMethods.isElementDisplayed(overviewScreenObjects.labelTotalDue);
					log.info(iosMethods.getValue(overviewScreenObjects.labelTotalDue));
				} catch (Exception e) {
					System.out.println(e.getMessage());
				}
			}
		}
		return isPresent;
	}


}
