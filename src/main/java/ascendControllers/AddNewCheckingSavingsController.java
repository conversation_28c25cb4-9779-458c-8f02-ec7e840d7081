package ascendControllers;

import ascendPageObjects.AddNewCheckingSavingsModal;
import ascendPageObjects.CommonMethodsPageObjects;
import base.Base;
import common.AndroidMethods;
import common.IOSMethods;
import jsonparsers.CommonParser;
import common.GlobalUtil;
import org.openqa.selenium.By;
import jsonparsers.TestDataParser;

import java.io.IOException;

public class AddNewCheckingSavingsController extends Base {
	public static String driverInstance;
	AndroidMethods androidMethods;
	IOSMethods iosMethods;
	AddNewCheckingSavingsModal addNewCheckingSavingsModal = new AddNewCheckingSavingsModal();
	CommonMethodsPageObjects commonMethodsPageObjects = new CommonMethodsPageObjects();
	static String accountHolderName;
	static String lastFourDigitsOfAccount;
	TestDataParser testDataParser = new TestDataParser();
	GlobalUtil globalUtil = new GlobalUtil();

	public AddNewCheckingSavingsController() throws IOException {
		// Get the current thread's driver
		if (Base.getAndroidDriver() != null) {
			androidMethods = new AndroidMethods(Base.getAndroidDriver());
			driverInstance = androidDriverInstance;
			addNewCheckingSavingsModal.androidElements();
			commonMethodsPageObjects.androidElements();

		} else if (Base.getIOSDriver() != null) {
			iosMethods = new IOSMethods(Base.getIOSDriver());
			driverInstance = iOSDriverInstance;
			addNewCheckingSavingsModal.iosElements();
			commonMethodsPageObjects.iosElements();

		} else {
			log.error("No valid driver instance found for Android or iOS.");
			throw new IllegalStateException("No valid driver instance found for Android or iOS.");

		}
	}

	public void addBankAccountWithoutSaving(String nickName, String accountNumber, String accountType) {
		log.info("addBankAccountIfDoesNotExist execution started");
		if (isIOS(Base.getIOSDriver())) {
			if (iosMethods != null) {
				iosMethods.staticWait(8000);
				iosMethods.isElementDisplayed(addNewCheckingSavingsModal.ddBankAccountType);
				this.enterBankAccountInfo(nickName, accountNumber, accountType);
				this.clickOnContinueButtonAddAccount();


			}
		} else {
			if (androidMethods != null) {
				androidMethods.isElementDisplayed(addNewCheckingSavingsModal.ddBankAccountType);
				androidMethods.staticWait(8000);
				log.info("clicked On AddNewCheckingsSavingsAccount");
				this.enterBankAccountInfo(nickName, accountNumber, accountType);
				this.clickOnContinueButtonAddAccount();
			}
		}
		log.info("addBankAccountIfDoesNotExist execution finished");

	}

	public void enterBankAccountInfo(String nickName, String accountNumber, String accountType) {
		log.info("enterBankAccountInfo execution started");
		this.enterAccountType(accountType);
		this.enterNickName(nickName);
		this.isUserAbleToEnterRoutingNumber();
		this.enterAccountNumber(accountNumber);
		log.info("enterBankAccountInfo execution finished");

	}


	public void clickOnContinueButtonAddAccount() {
		if (isIOS(Base.getIOSDriver())) {
			if (iosMethods != null) {
				iosMethods.scrollToAnElement(addNewCheckingSavingsModal.btnContinue, "DOWN");
				iosMethods.isElementDisplayed(addNewCheckingSavingsModal.btnContinue);
				iosMethods.isElementClickable(addNewCheckingSavingsModal.btnContinue, "addNewCheckingSavingsModal.btnContinue");
				iosMethods.tapAsWebElement(addNewCheckingSavingsModal.btnContinue);
				log.info("continue button clicked on add account modal");
				iosMethods.staticWait(5000);

			}
		} else {
			if (androidMethods != null) {
				androidMethods.staticWait(5000);
				androidMethods.scrollToAnElement(addNewCheckingSavingsModal.btnContinue, "DOWN");
				androidMethods.isElementPresent(addNewCheckingSavingsModal.btnContinue);
				androidMethods.isElementClickable(addNewCheckingSavingsModal.btnContinue, "payNowPaymentMethodObjects.btnContinue");
				androidMethods.tapAsWebElement(addNewCheckingSavingsModal.btnContinue);
				androidMethods.staticWait(5000);

			}
		}
	}

	public boolean isUserAbleToEnterNameOnBankAccount() {
		log.info("Entering name on bank account");
		boolean isPresent = false;
		accountHolderName = globalUtil.randomString(5);
		if (isAndroid(Base.getAndroidDriver())) {
			if (androidMethods != null) {
				androidMethods.isElementPresent(addNewCheckingSavingsModal.textBoxNameOnBankAccount);
				androidMethods.tapAsWebElement(addNewCheckingSavingsModal.textBoxNameOnBankAccount);
				androidMethods.clear(addNewCheckingSavingsModal.textBoxNameOnBankAccount);
				androidMethods.type(addNewCheckingSavingsModal.textBoxNameOnBankAccount, accountHolderName);
				if (androidMethods.getText(addNewCheckingSavingsModal.textBoxNameOnBankAccount).equalsIgnoreCase(accountHolderName))
					isPresent = true;
				//androidMethods.closeKeyPad();
				log.info("name on bank account entered");
				androidMethods.closeKeyPad();

			}
		} else {
			if (iosMethods != null) {
				iosMethods.isElementPresent(addNewCheckingSavingsModal.textBoxNameOnBankAccount);
				iosMethods.tapAsWebElement(addNewCheckingSavingsModal.textBoxNameOnBankAccount);
				iosMethods.clear(addNewCheckingSavingsModal.textBoxNameOnBankAccount);
				iosMethods.type(addNewCheckingSavingsModal.textBoxNameOnBankAccount, accountHolderName);
				if (iosMethods.getValue(addNewCheckingSavingsModal.textBoxNameOnBankAccount).equalsIgnoreCase(accountHolderName))
					isPresent = true;
				if (iosMethods.isElementPresent(commonMethodsPageObjects.btnDone))
					iosMethods.tapAsWebElement(commonMethodsPageObjects.btnDone);
				////androidMethods.closeKeyPad();
			}
		}
		return isPresent;
	}


	public boolean enterAccountType(String accountType) {
		log.info("enterAccountType execution started");
		boolean isPresent = false;
		if (isAndroid(Base.getAndroidDriver())) {
			if (androidMethods != null) {
				androidMethods.isElementDisplayed(addNewCheckingSavingsModal.ddBankAccountType);
				log.info("bank account type is displayed");
				androidMethods.tapAsWebElement(addNewCheckingSavingsModal.ddBankAccountType);
				androidMethods.tapAsWebElement(this.getAndroidAccountType(accountType));
			}
		} else {
			if (iosMethods != null) {
				log.info("entered account type: loop");
				iosMethods.staticWait(6000);
				iosMethods.tapAsWebElement(addNewCheckingSavingsModal.ddBankAccountType);
				iosMethods.tapAsWebElement(this.getAccountType(accountType));
			}
		}
		log.info("enterAccountType execution finished");
		return isPresent;
	}

	public By getAccountType(String accountType) {
		return By.xpath("//XCUIElementTypeButton[@name=\"" + accountType + "\"]");
	}

	public By getAndroidAccountType(String accountType) {
		return By.xpath("//android.widget.TextView[@text=\"" + accountType + "\"]");
	}


	public void enterNickName(String nickNameParam) {
		if (isAndroid(Base.getAndroidDriver())) {
			if (androidMethods != null) {
				androidMethods.isElementPresent(addNewCheckingSavingsModal.textBoxNickName);
				androidMethods.tapAsWebElement(addNewCheckingSavingsModal.textBoxNickName);
				androidMethods.clear(addNewCheckingSavingsModal.textBoxNickName);
				androidMethods.type(addNewCheckingSavingsModal.textBoxNickName, nickNameParam);
				androidMethods.closeKeyPad();

			}
		} else {
			if (iosMethods != null) {
				iosMethods.scrollToAnElement(addNewCheckingSavingsModal.textBoxNickName, "DOWN");
				iosMethods.isElementPresent(addNewCheckingSavingsModal.textBoxNickName);
				iosMethods.tapAsWebElement(addNewCheckingSavingsModal.textBoxNickName);
				iosMethods.clear(addNewCheckingSavingsModal.textBoxNickName);
				iosMethods.type(addNewCheckingSavingsModal.textBoxNickName, nickNameParam);
				if (iosMethods.isElementPresent(commonMethodsPageObjects.btnDone, 20))
					iosMethods.tapAsWebElement(commonMethodsPageObjects.btnDone);
			}
		}
		log.info("nickName entered ");


	}


	public boolean isUserAbleToEnterRoutingNumber() {
		int routingNumber;
		boolean isPresent = false;
		if (isAndroid(Base.getAndroidDriver())) {
			if (androidMethods != null) {
				androidMethods.scrollToAnElement(addNewCheckingSavingsModal.textBoxRoutingNumber, "DOWN");
				androidMethods.isElementPresent(addNewCheckingSavingsModal.textBoxRoutingNumber);
				androidMethods.tapAsWebElement(addNewCheckingSavingsModal.textBoxRoutingNumber);
				androidMethods.type(addNewCheckingSavingsModal.textBoxRoutingNumber, Long.toString(testDataParser.getRoutingNumber()));
				routingNumber = Integer.parseInt(androidMethods.getText(addNewCheckingSavingsModal.textBoxRoutingNumber));
				if (routingNumber == testDataParser.getRoutingNumber())
					isPresent = true;
				androidMethods.closeKeyPad();
			}
		} else {
			if (iosMethods != null) {
				iosMethods.isElementPresent(addNewCheckingSavingsModal.textBoxRoutingNumber);
				iosMethods.tapAsWebElement(addNewCheckingSavingsModal.textBoxRoutingNumber);
				log.info("entered routing number:)");
				try {
					log.info("entered routing number: " + testDataParser.getRoutingNumber());
				} catch (Exception e) {
					log.error("Error getting routing number: " + e.getMessage(), e);
				}
				iosMethods.type(addNewCheckingSavingsModal.textBoxRoutingNumber, String.valueOf(testDataParser.getRoutingNumber()));
				routingNumber = Integer.parseInt(iosMethods.getValue(addNewCheckingSavingsModal.textBoxRoutingNumber));
				if (routingNumber == testDataParser.getRoutingNumber())
					isPresent = true;
				if (iosMethods.isElementPresent(commonMethodsPageObjects.btnDone, 20))
					iosMethods.tapAsWebElement(commonMethodsPageObjects.btnDone);
			}
		}
		log.info("Routing number entered ");
		return isPresent;
	}


	public boolean enterAccountNumber(String accountNumber) {
		boolean isPresent = false;
		log.info(accountNumber);
		String accountNumberFromScreen;
		lastFourDigitsOfAccount = accountNumber.substring(accountNumber.length() - 4);
		log.info(lastFourDigitsOfAccount);
		if (isAndroid(Base.getAndroidDriver())) {
			if (androidMethods != null) {
				androidMethods.scrollToAnElement(addNewCheckingSavingsModal.textBoxBankAccountNumber, "DOWN");
				androidMethods.isElementPresent(addNewCheckingSavingsModal.textBoxBankAccountNumber);
				androidMethods.tapAsWebElement(addNewCheckingSavingsModal.textBoxBankAccountNumber);
				androidMethods.type(addNewCheckingSavingsModal.textBoxBankAccountNumber, accountNumber);
				androidMethods.isElementPresent(addNewCheckingSavingsModal.textBoxBankAccountNumber);
				accountNumberFromScreen = androidMethods.getText(addNewCheckingSavingsModal.textBoxBankAccountNumber);
				if (accountNumberFromScreen.equals(accountNumber))
					isPresent = true;
				androidMethods.closeKeyPad();
				androidMethods.staticWait(10000);
			}
		} else {
			if (iosMethods != null) {
				iosMethods.scrollToAnElement(addNewCheckingSavingsModal.textBoxBankAccountNumber, "DOWN");
				iosMethods.isElementPresent(addNewCheckingSavingsModal.textBoxBankAccountNumber);
				iosMethods.tapAsWebElement(addNewCheckingSavingsModal.textBoxBankAccountNumber);
				iosMethods.type(addNewCheckingSavingsModal.textBoxBankAccountNumber, accountNumber);
				iosMethods.isElementPresent(addNewCheckingSavingsModal.textBoxBankAccountNumber);
				accountNumberFromScreen = iosMethods.getValue(addNewCheckingSavingsModal.textBoxBankAccountNumber);
				if (accountNumberFromScreen.equals(accountNumber))
					isPresent = true;
				if (iosMethods.isElementPresent(commonMethodsPageObjects.btnDone, 20))
					iosMethods.tapAsWebElement(commonMethodsPageObjects.btnDone);

			}
		}
		log.info("enter account number completed");
		return isPresent;
	}


}
