package ascendControllers;

import ascendPageObjects.PayNowConfirmationPageObjects;
import ascendPageObjects.PayNowReviewPaymentPageObjects;
import base.Base;
import common.AndroidMethods;
import common.IOSMethods;
import jsonparsers.PayNowParser;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class PayNowConfirmationController extends Base {
	public static String driverInstance;
	AndroidMethods androidMethods;
	IOSMethods iosMethods;
	PayNowConfirmationPageObjects payNowConfirmationPageObjects = new PayNowConfirmationPageObjects();
	PayNowParser payParser = new PayNowParser();

	String paymentMethodTxt = PayNowParser.getPaymentMethodTxt();

	String paymentAmountTxt = PayNowParser.getAmountText();

	String paymentDateTxt = PayNowParser.getPaymentDateTxt();

	String notificationTxt = PayNowParser.getNotificationTxt();

	String confirmPaymentTxt = PayNowParser.getConfirmPaymentTxt();
	String confirmPageTitleTxt = PayNowParser.getConfirmPageTitleTxt();

	String confirmationDisclaimerTxt = payParser.getConfirmationDisclaimer();

	String successMessageTxt = payParser.getSuccessMessage();

	String goBackHomeTxt = payParser.getGoBackHomeTxt();
	String amountPaidTxt = PayNowParser.getAmountPaidTxt();
	String amountPaidTxtAndroid = PayNowParser.getAmountPaidTxtAnd();

	String notificationMsgTxt = payParser.getNotificationMsgTxt();

	String remainingBalanceTxt = PayNowParser.getRemainingBalanceTxt();

	String paymentDisclaimer1 = payParser.getPaymentDisclaimer1Txt();
	String paymentDisclaimer = payParser.getPaymentDisclaimerTxt();

	String manageAutoPayTxt = PayNowParser.getManageAutoPayTxt();

	String manageAutoPayHomeTxt = PayNowParser.getManageAutoPayAccountTxt();
	String btnModifyArrangementTxt = PayNowParser.getbtnModifyArrangementTxt();

	public PayNowConfirmationController() throws IOException {
		// Get the current thread's driver
		if (Base.getAndroidDriver() != null) {
			androidMethods = new AndroidMethods(Base.getAndroidDriver());
			driverInstance = androidDriverInstance;
			payNowConfirmationPageObjects.androidElements();

		} else if (Base.getIOSDriver() != null) {
			iosMethods = new IOSMethods(Base.getIOSDriver());
			driverInstance = iOSDriverInstance;
			payNowConfirmationPageObjects.iosElements();

		} else {
			log.error("No valid driver instance found for Android or iOS.");
			throw new IllegalStateException("No valid driver instance found for Android or iOS.");

		}
	}

	//Verification of Payment Success Message
	public boolean verifyPaymentSuccessMessage() {
		boolean isPresent = false;
		if (isIOS(Base.getIOSDriver())) {
			if (iosMethods != null) {
				if (iosMethods.isElementDisplayed(payNowConfirmationPageObjects.dismissButton, 30)) {
					log.info("Dismiss popup is displayed");
					iosMethods.tapAsWebElement(payNowConfirmationPageObjects.dismissButton);
					log.info("Dismiss popup is tapped");
				} else {
					log.info("Dismiss popup is not displayed");
				}
				iosMethods.isElementPresent(payNowConfirmationPageObjects.paymentSuccessMessage);
				if (iosMethods.getText(payNowConfirmationPageObjects.paymentSuccessMessage).equals(successMessageTxt))
					isPresent = true;
				log.info(iosMethods.getText(payNowConfirmationPageObjects.paymentSuccessMessage));
			}
		} else {
			if (androidMethods != null) {
				if (androidMethods.isElementPresent(payNowConfirmationPageObjects.closeButton)) {
					androidMethods.tapAsWebElement(payNowConfirmationPageObjects.closeButton);
				}
				androidMethods.isElementPresent(payNowConfirmationPageObjects.paymentSuccessMessage);
				if (androidMethods.getText(payNowConfirmationPageObjects.paymentSuccessMessage).equals(successMessageTxt))
					isPresent = true;
				log.info(androidMethods.getText(payNowConfirmationPageObjects.paymentSuccessMessage));
			}
		}
		return isPresent;
	}


	//Verification of Payment Confirmation number
	public boolean verifyConfirmationNumber() {
		boolean isPresent = false;
		if (isIOS(Base.getIOSDriver())) {
			if (iosMethods != null) {
				iosMethods.isElementPresent(payNowConfirmationPageObjects.copyConfirmationNumber);
				if (iosMethods.isElementPresent(payNowConfirmationPageObjects.copyConfirmationNumber))
					isPresent = true;
				log.info(iosMethods.getText(payNowConfirmationPageObjects.copyConfirmationNumber));
			}
		} else {
			if (androidMethods != null) {
				androidMethods.isElementPresent(payNowConfirmationPageObjects.copyConfirmationNumber);
				if (androidMethods.isElementPresent(payNowConfirmationPageObjects.copyConfirmationNumber))
					isPresent = true;
				log.info(androidMethods.getText(payNowConfirmationPageObjects.copyConfirmationNumber));
			}
		}
		return isPresent;
	}

	//Verification of GoBackToOverView Link
	public boolean verifyGoBackToOverviewLink() {
		boolean isPresent = false;
		if (isIOS(Base.getIOSDriver())) {
			if (iosMethods != null) {
				iosMethods.isElementPresent(payNowConfirmationPageObjects.linkGoBackToOverview);
				if (iosMethods.getText(payNowConfirmationPageObjects.linkGoBackToOverview).equals(goBackHomeTxt))
					isPresent = true;
				log.info(iosMethods.getText(payNowConfirmationPageObjects.linkGoBackToOverview));
			}
		} else {
			if (androidMethods != null) {
				androidMethods.isElementPresent(payNowConfirmationPageObjects.linkGoBackToOverview);
				if (androidMethods.getText(payNowConfirmationPageObjects.linkGoBackToOverview).equals(goBackHomeTxt))
					isPresent = true;
				log.info(androidMethods.getText(payNowConfirmationPageObjects.linkGoBackToOverview));
			}
		}
		return isPresent;
	}

	//Verification of Confirmation notification copy
	public boolean verifyConfirmationNotificationCopy() {
		boolean isPresent = false;
		if (isIOS(Base.getIOSDriver())) {
			if (iosMethods != null) {
				iosMethods.isElementPresent(payNowConfirmationPageObjects.confirmationNotificationMessage);
				if (iosMethods.getText(payNowConfirmationPageObjects.confirmationNotificationMessage).equals(notificationMsgTxt))
					isPresent = true;
				log.info(iosMethods.getText(payNowConfirmationPageObjects.confirmationNotificationMessage));
			}
		} else {
			if (androidMethods != null) {
				androidMethods.isElementPresent(payNowConfirmationPageObjects.confirmationNotificationMessage);
				if (androidMethods.getText(payNowConfirmationPageObjects.confirmationNotificationMessage).equals(notificationMsgTxt))
					isPresent = true;
				log.info(androidMethods.getText(payNowConfirmationPageObjects.confirmationNotificationMessage));
			}
		}
		return isPresent;
	}


	public boolean verifyEmailNotificationOnConfirmationPage(String email) {
		boolean isPresent = false;
		if (isIOS(Base.getIOSDriver())) {
			if (iosMethods != null) {
				iosMethods.isElementPresent(payNowConfirmationPageObjects.emailNotificationConfirmPaymentPg);
				if (iosMethods.getText(payNowConfirmationPageObjects.emailNotificationConfirmPaymentPg).equals(email))
					isPresent = true;
			}
		} else {
			if (androidMethods != null) {
				androidMethods.isElementPresent(payNowConfirmationPageObjects.emailNotificationConfirmPaymentPg);
				if (androidMethods.getText(payNowConfirmationPageObjects.emailNotificationConfirmPaymentPg).equals(email))
					isPresent = true;
			}
		}
		return isPresent;
	}

	public boolean verifyPhoneNotificationOnConfirmationPage(String phone) {
		boolean isPresent = false;
		if (isIOS(Base.getIOSDriver())) {
			if (iosMethods != null) {
				iosMethods.isElementPresent(payNowConfirmationPageObjects.phoneNotificationConfirmPaymentPg);
				if (iosMethods.getText(payNowConfirmationPageObjects.phoneNotificationConfirmPaymentPg).equals(phone))
					isPresent = true;
			}
		} else {
			if (androidMethods != null) {
				androidMethods.isElementPresent(payNowConfirmationPageObjects.phoneNotificationConfirmPaymentPg);
				if (androidMethods.getText(payNowConfirmationPageObjects.phoneNotificationConfirmPaymentPg).equals(phone))
					isPresent = true;
			}
		}
		return isPresent;
	}

	//Verification of PaymentMethod blade on confirmation page
	public boolean verifyPaymentMethodBladeOnConfirmationPage() {
		boolean isPresent = false;
		if (isIOS(Base.getIOSDriver())) {
			if (iosMethods != null) {
				iosMethods.isElementPresent(payNowConfirmationPageObjects.bladePaymentMethod);
				if (iosMethods.getText(payNowConfirmationPageObjects.bladePaymentMethod).equals(paymentMethodTxt))
					isPresent = true;
				log.info(iosMethods.getText(payNowConfirmationPageObjects.bladePaymentMethod));
			}
		} else {
			if (androidMethods != null) {
				androidMethods.isElementPresent(payNowConfirmationPageObjects.bladePaymentMethod);
				if (androidMethods.getText(payNowConfirmationPageObjects.bladePaymentMethod).equals(paymentMethodTxt))
					isPresent = true;
				log.info(androidMethods.getText(payNowConfirmationPageObjects.bladePaymentMethod));
			}
		}
		return isPresent;
	}

	//Verification of PaymentMethod copy on confirmation page
	public boolean verifyPaymentMethodCopyOnConfirmationPage(String account) {
		boolean isPresent = false;
		if (isIOS(Base.getIOSDriver())) {
			if (iosMethods != null) {
				iosMethods.isElementPresent(payNowConfirmationPageObjects.copyPaymentMethod);
				if (iosMethods.getText(payNowConfirmationPageObjects.copyPaymentMethod).equals(account))
					isPresent = true;
				log.info(iosMethods.getText(payNowConfirmationPageObjects.copyPaymentMethod));
			}
		} else {
			if (androidMethods != null) {
				androidMethods.isElementPresent(payNowConfirmationPageObjects.copyPaymentMethod);
				androidMethods.isElementDisplayed(payNowConfirmationPageObjects.copyPaymentMethod);
				if (androidMethods.getText(payNowConfirmationPageObjects.copyPaymentMethod).equals(account))
					isPresent = true;
				log.info(androidMethods.getText(payNowConfirmationPageObjects.copyPaymentMethod));
			}
		}
		return isPresent;
	}

	public boolean verifyAmountPaidBladeOnConfirmationPage() {
		boolean isPresent = false;
		if (isIOS(Base.getIOSDriver())) {
			if (iosMethods != null) {
				iosMethods.isElementPresent(payNowConfirmationPageObjects.bladeAmountPaid);
				if (iosMethods.getText(payNowConfirmationPageObjects.bladeAmountPaid).equals(amountPaidTxt))
					isPresent = true;
				log.info(iosMethods.getText(payNowConfirmationPageObjects.bladeAmountPaid));
			}
		} else {
			if (androidMethods != null) {
				androidMethods.isElementPresent(payNowConfirmationPageObjects.bladeAmountPaid);
				if (androidMethods.getText(payNowConfirmationPageObjects.bladeAmountPaid).equals("Amount Paid"))
					isPresent = true;
				log.info(androidMethods.getText(payNowConfirmationPageObjects.bladeAmountPaid));
			}
		}
		return isPresent;
	}

	//Verification of Amount paid copy on confirmation page
	public boolean verifyAmountPaidCopyOnConfirmationPage(String amount) {
		boolean isPresent = false;
		if (isIOS(Base.getIOSDriver())) {
			if (iosMethods != null) {
				iosMethods.isElementPresent(payNowConfirmationPageObjects.moneyAmountPaid);
				iosMethods.isElementDisplayed(payNowConfirmationPageObjects.moneyAmountPaid);
				if (iosMethods.getText(payNowConfirmationPageObjects.moneyAmountPaid).equals(amount)) {
					isPresent = true;
				}
			}
		} else {
			if (androidMethods != null) {
				androidMethods.isElementPresent(payNowConfirmationPageObjects.moneyAmountPaid);
				androidMethods.isElementDisplayed(payNowConfirmationPageObjects.moneyAmountPaid);
				if (androidMethods.getText(payNowConfirmationPageObjects.moneyAmountPaid).equals(amount)) {
					isPresent = true;
				}
			}
		}
		return isPresent;
	}

	//Verification of PaymentDate blade on confirmation page
	public boolean verifyPaymentDateBladeOnConfirmationPage() {
		boolean isPresent = false;
		if (isIOS(Base.getIOSDriver())) {
			if (iosMethods != null) {
				iosMethods.isElementPresent(payNowConfirmationPageObjects.bladePaymentDate);
				if (iosMethods.getText(payNowConfirmationPageObjects.bladePaymentDate).equals(paymentDateTxt))
					isPresent = true;
				log.info(iosMethods.getText(payNowConfirmationPageObjects.bladePaymentDate));
			}
		} else {
			if (androidMethods != null) {
				androidMethods.isElementPresent(payNowConfirmationPageObjects.bladePaymentDate);
				if (androidMethods.getText(payNowConfirmationPageObjects.bladePaymentDate).equals(paymentDateTxt))
					isPresent = true;
				log.info(androidMethods.getText(payNowConfirmationPageObjects.bladePaymentDate));
			}
		}
		return isPresent;
	}

	//Verification of PaymentDate on confirmation page
	public boolean verifyPaymentDateOnConfirmationPage() {
		Date currentDate = new Date();
// Define the desired date format
		SimpleDateFormat dateFormat = new SimpleDateFormat("MM/dd/yy");
		// Format the current date
		String formattedDate = dateFormat.format(currentDate);
		boolean isPresent = false;
		if (isIOS(Base.getIOSDriver())) {
			if (iosMethods != null) {
				iosMethods.isElementPresent(payNowConfirmationPageObjects.paymentDate);
				iosMethods.isElementPresent(payNowConfirmationPageObjects.paymentDate);
				if (iosMethods.getText(payNowConfirmationPageObjects.paymentDate).equals(formattedDate))
					isPresent = true;
				log.info(iosMethods.getText(payNowConfirmationPageObjects.paymentDate));
			}
		} else {
			if (androidMethods != null) {
				androidMethods.isElementPresent(payNowConfirmationPageObjects.copyPaymentDate);
				androidMethods.isElementPresent(payNowConfirmationPageObjects.copyPaymentDate);
				if (androidMethods.getText(payNowConfirmationPageObjects.copyPaymentDate).equals(formattedDate))
					isPresent = true;
			}
		}
		return isPresent;
	}

	//Verification of Remaining balance blade on confirmation page
	public boolean verifyRemainingBalanceBladeOnConfirmationPage() {
		boolean isPresent = false;
		if (isIOS(Base.getIOSDriver())) {
			if (iosMethods != null) {
				iosMethods.scrollToAnElement(payNowConfirmationPageObjects.bladeRemainingBalance, "DOWN");
				iosMethods.isElementPresent(payNowConfirmationPageObjects.bladeRemainingBalance);
				if (iosMethods.getText(payNowConfirmationPageObjects.bladeRemainingBalance).equals(remainingBalanceTxt))
					isPresent = true;
				log.info(iosMethods.getText(payNowConfirmationPageObjects.bladeRemainingBalance));
			}
		} else {
			if (androidMethods != null) {
				androidMethods.scrollToAnElement(payNowConfirmationPageObjects.bladeRemainingBalance, "DOWN");
				androidMethods.isElementPresent(payNowConfirmationPageObjects.bladeRemainingBalance);
				if (androidMethods.getText(payNowConfirmationPageObjects.bladeRemainingBalance).equals(remainingBalanceTxt))
					isPresent = true;
				log.info(androidMethods.getText(payNowConfirmationPageObjects.bladeRemainingBalance));
			}
		}
		return isPresent;
	}

	//Verification of Remaining balance on confirmation page
	public boolean verifyRemainingBalanceOnConfirmationPage(String amount, String totalDue) {
		double amountValue = Double.parseDouble(amount.replace("$", ""));
		double totalDueValue = Double.parseDouble(totalDue.replace("$", ""));
		double remainingBalance = totalDueValue - amountValue;
		String remainingBalanceStr = String.valueOf(remainingBalance);
		remainingBalanceStr = "$" + remainingBalanceStr;
		boolean isPresent = false;
		if (isIOS(Base.getIOSDriver())) {
			if (iosMethods != null) {
				iosMethods.scrollToAnElement(payNowConfirmationPageObjects.moneyRemainingBalance, "DOWN");
				iosMethods.isElementPresent(payNowConfirmationPageObjects.moneyRemainingBalance);
				iosMethods.isElementDisplayed(payNowConfirmationPageObjects.moneyRemainingBalance);
				if (iosMethods.getText(payNowConfirmationPageObjects.moneyRemainingBalance).equals(remainingBalanceStr))
					isPresent = true;
				log.info(iosMethods.getText(payNowConfirmationPageObjects.moneyRemainingBalance));
				log.info(remainingBalanceStr);
			}
		} else {
			if (androidMethods != null) {
				androidMethods.scrollToAnElement(payNowConfirmationPageObjects.moneyRemainingBalance, "DOWN");
				androidMethods.isElementPresent(payNowConfirmationPageObjects.moneyRemainingBalance);
				androidMethods.isElementDisplayed(payNowConfirmationPageObjects.moneyRemainingBalance);
				if (androidMethods.getText(payNowConfirmationPageObjects.moneyRemainingBalance).equals(remainingBalanceStr))
					isPresent = true;
				log.info(androidMethods.getText(payNowConfirmationPageObjects.moneyRemainingBalance));
				log.info(remainingBalanceStr);
			}
		}
		return isPresent;
	}

	//Verification of Payment Confirmation Disclaimer Copy on confirmation page
	public boolean verifyPaymentConfirmationDisclaimerCopyOnConfirmationPage() {
		boolean isPresent = false;
		if (isIOS(Base.getIOSDriver())) {
			if (iosMethods != null) {
				iosMethods.scrollOnce("DOWN");
				isPresent = iosMethods.isElementDisplayed(payNowConfirmationPageObjects.paymentConfirmationDisclaimer);

			}
		} else {
			if (androidMethods != null) {
				androidMethods.scrollOnce("DOWN");
				androidMethods.scrollToAnElement(payNowConfirmationPageObjects.paymentConfirmationDisclaimer, "DOWN");
				isPresent = androidMethods.isElementDisplayed(payNowConfirmationPageObjects.paymentConfirmationDisclaimer);
//                if (androidMethods.getText(payNowConfirmationPageObjects.paymentConfirmationDisclaimer).equals(payNowConfirmationPageObjects.comparePaymentConfirmationDisclaimer) || androidMethods.getText(payNowConfirmationPageObjects.paymentConfirmationDisclaimer).equals(paymentDisclaimer1))
				log.info(androidMethods.getText(payNowConfirmationPageObjects.paymentConfirmationDisclaimer));
			}
		}
		return isPresent;
	}


	//Click functionality of Go Back To Overview link
	public void clickOnGoBackToOverviewLink() {
		if (isIOS(Base.getIOSDriver())) {
			if (iosMethods != null) {
				iosMethods.staticWait(8000);
				iosMethods.scrollToAnElement(payNowConfirmationPageObjects.linkGoBackToOverview, "UP");
				iosMethods.tapAsWebElement(payNowConfirmationPageObjects.linkGoBackToOverview);
				iosMethods.staticWait(8000);
			}
		} else {
			if (androidMethods != null) {
				androidMethods.scrollToAnElement(payNowConfirmationPageObjects.linkGoBackToOverview, "UP");
				androidMethods.isElementPresent(payNowConfirmationPageObjects.linkGoBackToOverview);
				androidMethods.tapAsWebElement(payNowConfirmationPageObjects.linkGoBackToOverview);
				androidMethods.staticWait(8000);
			}
		}
	}

}
