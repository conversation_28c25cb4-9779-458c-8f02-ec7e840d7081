package ascendControllers;

import base.Base;
import common.AndroidMethods;
import common.IOSMethods;
import ascendPageObjects.PayNowSelectPaymentMethodObjects;

import java.io.IOException;

public class PayNowSelectPaymentMethodScreenController extends Base {
	public static String driverInstance;
	AndroidMethods androidMethods;
	IOSMethods iosMethods;
	PayNowSelectPaymentMethodObjects payNowSelectPaymentMethodObjectsObjects = new PayNowSelectPaymentMethodObjects();

	public PayNowSelectPaymentMethodScreenController() throws IOException {
		// Get the current thread's driver
		if (Base.getAndroidDriver() != null) {
			androidMethods = new AndroidMethods(Base.getAndroidDriver());
			driverInstance = androidDriverInstance;
			payNowSelectPaymentMethodObjectsObjects.androidElements();

		} else if (Base.getIOSDriver() != null) {
			iosMethods = new IOSMethods(Base.getIOSDriver());
			driverInstance = iOSDriverInstance;
			payNowSelectPaymentMethodObjectsObjects.iosElements();

		} else {
			log.error("No valid driver instance found for Android or iOS.");
			throw new IllegalStateException("No valid driver instance found for Android or iOS.");

		}
	}


	public void clickCheckingAndSavingsAccountAndContinue() {
		if (isAndroid(Base.getAndroidDriver())) {
			if (androidMethods != null) {
				androidMethods.isElementPresent(payNowSelectPaymentMethodObjectsObjects.checkingAndSavingsAcc);
				androidMethods.isElementClickable(payNowSelectPaymentMethodObjectsObjects.checkingAndSavingsAcc, "Checking and Savings Account");
				androidMethods.tapAsWebElement(payNowSelectPaymentMethodObjectsObjects.checkingAndSavingsAcc);
				androidMethods.isElementClickable(payNowSelectPaymentMethodObjectsObjects.continueBtn, "Continue Button");
				androidMethods.tapAsWebElement(payNowSelectPaymentMethodObjectsObjects.continueBtn);
			}
		} else {
			if (iosMethods != null) {
				iosMethods.isElementPresent(payNowSelectPaymentMethodObjectsObjects.checkingAndSavingsAcc);
				iosMethods.tapAsWebElement(payNowSelectPaymentMethodObjectsObjects.checkingAndSavingsAcc);
				iosMethods.isElementClickable(payNowSelectPaymentMethodObjectsObjects.continueBtn, "Continue Button");
				iosMethods.tapAsWebElement(payNowSelectPaymentMethodObjectsObjects.continueBtn);
			}
		}
	}


}
